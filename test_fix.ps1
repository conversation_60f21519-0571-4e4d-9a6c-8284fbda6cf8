# Test script to validate the /detailed-analyze fix
# This script starts the server, runs the comparison test, and stops the server

Write-Host "🚀 Testing the /detailed-analyze endpoint fix..." -ForegroundColor Green
Write-Host "=" * 60

# Check if the executable exists
if (-not (Test-Path ".\image-inspector.exe")) {
    Write-Host "❌ image-inspector.exe not found. Building..." -ForegroundColor Red
    go build -o image-inspector.exe ./cmd/api
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Build failed!" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Build successful" -ForegroundColor Green
}

# Start the server in background
Write-Host "`n🔧 Starting the image inspector server..." -ForegroundColor Yellow
$serverProcess = Start-Process -FilePath ".\image-inspector.exe" -PassThru -WindowStyle Hidden

# Wait for server to start
Write-Host "⏳ Waiting for server to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# Test if server is running
try {
    $healthCheck = Invoke-RestMethod -Uri "http://localhost:8080/health" -Method GET -TimeoutSec 5
    Write-Host "✅ Server is running (Status: $($healthCheck.status))" -ForegroundColor Green
} catch {
    Write-Host "❌ Server failed to start or health check failed" -ForegroundColor Red
    if ($serverProcess -and -not $serverProcess.HasExited) {
        Stop-Process -Id $serverProcess.Id -Force
    }
    exit 1
}

# Run the comparison test
Write-Host "`n🧪 Running endpoint comparison test..." -ForegroundColor Yellow
Write-Host "=" * 60

try {
    & .\test_endpoints_comparison.ps1
    $testResult = $LASTEXITCODE
} catch {
    Write-Host "❌ Test script failed: $($_.Exception.Message)" -ForegroundColor Red
    $testResult = 1
}

# Stop the server
Write-Host "`n🛑 Stopping the server..." -ForegroundColor Yellow
if ($serverProcess -and -not $serverProcess.HasExited) {
    Stop-Process -Id $serverProcess.Id -Force
    Write-Host "✅ Server stopped" -ForegroundColor Green
} else {
    Write-Host "⚠️  Server was already stopped" -ForegroundColor Yellow
}

# Final result
Write-Host "`n" + "=" * 60
if ($testResult -eq 0) {
    Write-Host "🎉 ALL TESTS PASSED! The fix is working correctly." -ForegroundColor Green
    Write-Host "✅ /detailed-analyze now returns correct image dimensions" -ForegroundColor Green
    Write-Host "✅ Both endpoints return consistent results" -ForegroundColor Green
} else {
    Write-Host "❌ TESTS FAILED! The fix needs more work." -ForegroundColor Red
    Write-Host "Please check the test output above for specific issues." -ForegroundColor Yellow
}

Write-Host "`nTest completed." -ForegroundColor Cyan
exit $testResult
